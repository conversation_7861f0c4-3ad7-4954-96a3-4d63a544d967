# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  archive:
    dependency: transitive
    description:
      name: archive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.11"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.2"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.4.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.2"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.14.11"
  connectivity:
    dependency: "direct main"
    description:
      name: connectivity
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.5+2"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.2"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_spinkit:
    dependency: "direct main"
    description:
      name: flutter_spinkit
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  geolocator:
    dependency: "direct main"
    description:
      name: geolocator
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.1.4+2"
  google_api_availability:
    dependency: transitive
    description:
      name: google_api_availability
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  google_maps_flutter:
    dependency: "direct main"
    description:
      name: google_maps_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.21+8"
  google_maps_webservice:
    dependency: "direct main"
    description:
      name: google_maps_webservice
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.16"
  http:
    dependency: transitive
    description:
      name: http
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.0+2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.3"
  image:
    dependency: transitive
    description:
      name: image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.4"
  location:
    dependency: "direct main"
    description:
      name: location
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.5"
  location_permissions:
    dependency: transitive
    description:
      name: location_permissions
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.3"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.6"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.8"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.6.4"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0+1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.4.0"
  pin_code_text_field:
    dependency: "direct main"
    description:
      name: pin_code_text_field
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.0"
  provider:
    dependency: "direct main"
    description:
      name: provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0+1"
  quiver:
    dependency: transitive
    description:
      name: quiver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.4"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sliding_up_panel:
    dependency: "direct main"
    description:
      name: sliding_up_panel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.6"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.5"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.9.3"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.11"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.6"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.8"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.5.0"
sdks:
  dart: ">=2.4.0 <3.0.0"
  flutter: ">=1.6.7 <2.0.0"
